{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:08:57","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:08:57","url":"/unknown-route"}
{"contentLength":"599","duration":"54ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:10:24","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:10:24","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 14:11:11"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 14:11:11"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:11:55","url":"/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"113","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:11:55","url":"/"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:11:55 +0000] \"GET / HTTP/1.1\" 200 113 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:11:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:12:09","url":"/health/live","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:12:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:12:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:12:09"}
{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:15:47","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:15:47","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 14:18:09"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 14:18:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 14:18:45","url":"/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"113","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 14:18:45","url":"/"}
{"level":"info","message":"::1 - - [15/Jul/2025:07:18:45 +0000] \"GET / HTTP/1.1\" 200 113 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 14:18:45"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:20:51","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:20:51","url":"/unknown-route"}
{"contentLength":"599","duration":"82ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:22:04","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:22:04","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:25:52","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:25:52","url":"/unknown-route"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 17:30:51"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 17:30:51"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:31:16","url":"/auth/","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"contentLength":"85","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 17:31:16","url":"/auth/"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:31:16 +0000] \"GET /auth/ HTTP/1.1\" 404 85 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 17:31:16"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"/health","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 17:31:26","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"50ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-15 17:31:26","totalServices":4}
{"contentLength":"604","duration":"52ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:31:26","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:31:26 +0000] \"GET /health HTTP/1.1\" 503 604 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652\"","service":"api-gateway","timestamp":"2025-07-15 17:31:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"/health","userAgent":"axios/1.10.0"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 17:32:46","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"136ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-15 17:32:46","totalServices":4}
{"contentLength":"606","duration":"136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:32:46","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:32:46 +0000] \"GET /health HTTP/1.1\" 503 606 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:32:46"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"/health","userAgent":"axios/1.10.0"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"33ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-15 17:33:26","totalServices":4}
{"contentLength":"605","duration":"35ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:26","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:33:26 +0000] \"GET /health HTTP/1.1\" 503 605 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:33:26"}
{"contentLength":"57","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 17:33:26","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 17:33:26"}
{"contentLength":"1470","duration":"131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:33:27 +0000] \"POST /auth/register HTTP/1.1\" 503 1470 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:33:27"}
{"contentLength":"42","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 17:33:27","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 17:33:27"}
{"contentLength":"238","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"level":"info","message":"::1 - - [15/Jul/2025:10:33:27 +0000] \"POST /auth/register HTTP/1.1\" 400 238 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 17:33:27"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 18:47:32"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 18:47:32"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"api-gateway","timestamp":"2025-07-15 18:47:44"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 19:02:15"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 19:02:15"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 19:04:02"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 19:04:02"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-16 03:55:00"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-16 03:55:00"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-16 03:57:28","url":"/health","userAgent":"axios/1.10.0"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for assessment-service","service":"api-gateway","timestamp":"2025-07-16 03:57:28","url":"http://localhost:3003"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for archive-service","service":"api-gateway","timestamp":"2025-07-16 03:57:28","url":"http://localhost:3002"}
{"code":"ECONNREFUSED","error":"","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-16 03:57:28","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"252ms","service":"api-gateway","servicesHealthy":1,"timestamp":"2025-07-16 03:57:28","totalServices":4}
{"contentLength":"606","duration":"254ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:28","url":"/health"}
{"level":"info","message":"::1 - - [15/Jul/2025:20:57:28 +0000] \"GET /health HTTP/1.1\" 503 606 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-16 03:57:28"}
{"contentLength":"64","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-16 03:57:28","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/register","service":"api-gateway","timestamp":"2025-07-16 03:57:28"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registration successful","service":"api-gateway","timestamp":"2025-07-16 03:57:28","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"contentLength":"581","duration":"306ms","ip":"::1","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":201,"timestamp":"2025-07-16 03:57:28","url":"/auth/register"}
{"level":"info","message":"::1 - - [15/Jul/2025:20:57:28 +0000] \"POST /auth/register HTTP/1.1\" 201 581 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-16 03:57:28"}
{"contentLength":"64","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-16 03:57:28","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/login","service":"api-gateway","timestamp":"2025-07-16 03:57:28"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"contentLength":"569","duration":"243ms","ip":"::1","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-16 03:57:29","url":"/auth/login"}
{"level":"info","message":"::1 - - [15/Jul/2025:20:57:29 +0000] \"POST /auth/login HTTP/1.1\" 200 569 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-16 03:57:29","url":"/auth/profile","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"GET","path":"/profile","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"level":"info","message":"Proxying auth request to: http://localhost:3001/auth/profile","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"contentLength":"210","duration":"11ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-16 03:57:29","url":"/auth/profile","userEmail":"<EMAIL>","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"level":"info","message":"::1 - - [15/Jul/2025:20:57:29 +0000] \"GET /auth/profile HTTP/1.1\" 200 210 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"contentLength":"546","contentType":"application/json","ip":"::1","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-16 03:57:29","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"level":"info","message":"Proxying assessment request to: http://localhost:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"contentLength":"114","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:29","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"level":"info","message":"::1 - - [15/Jul/2025:20:57:29 +0000] \"POST /assessments/submit HTTP/1.1\" 503 114 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 21:49:00"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 21:49:00"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:49:05","url":"/health/live"}
{"contentLength":"57","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:49:05","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:49:05 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:49:05"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:49:35","url":"/health/live"}
{"contentLength":"57","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:49:35","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:49:35 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:49:35"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:50:05","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:50:05","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:50:05 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:50:05"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:50:35","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:50:35","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:50:35 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:50:35"}
{"contentLength":"61","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 21:51:01","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 21:51:01"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"contentLength":"118","duration":"3955ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:05","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:21:51:05 +0000] \"POST /auth/register HTTP/1.1\" 503 118 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"contentLength":"61","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 21:51:05","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:51:05","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:51:05","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:51:05 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:09"}
{"contentLength":"118","duration":"3940ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:09","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:21:51:09 +0000] \"POST /auth/login HTTP/1.1\" 503 118 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 21:51:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:51:35","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:51:35","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:51:35 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:51:35"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:52:05","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:52:05","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:52:05 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:52:05"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:52:35","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:52:35","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:52:35 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:52:35"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:53:05","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:53:05","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:53:05 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:53:05"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:53:35","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:53:35","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:53:35 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:53:35"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:54:06","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:54:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:54:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:54:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:54:36","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:54:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:54:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:54:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:55:06","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:55:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:55:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:55:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:55:36","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:55:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:55:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:55:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:56:06","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:56:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:56:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:56:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:56:36","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:56:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:56:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:56:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:57:06","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:57:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:57:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:57:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:57:36","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:57:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:57:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:57:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:58:06","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:58:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:58:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:58:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:58:36","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:58:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:58:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:58:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:59:06","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:59:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:59:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:59:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 21:59:36","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 21:59:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:21:59:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 21:59:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:00:06","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:00:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:00:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:00:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:00:36","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:00:36","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:00:36 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:00:36"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:01:06","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:01:06","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:01:06 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:01:06"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:01:37","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:01:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:01:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:01:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:02:07","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:02:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:02:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:02:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:02:37","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:02:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:02:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:02:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:03:07","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:03:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:03:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:03:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:03:37","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:03:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:03:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:03:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:04:07","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:04:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:04:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:04:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:04:37","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:04:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:04:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:04:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:05:07","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:05:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:05:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:05:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:05:37","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:05:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:05:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:05:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:06:07","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:06:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:06:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:06:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:06:37","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:06:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:06:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:06:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:07:07","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:07:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:07:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:07:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:07:37","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:07:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:07:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:07:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:08:07","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:08:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:08:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:08:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:08:37","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:08:37","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:08:37 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:08:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:09:07","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:09:07","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:09:07 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:09:07"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:09:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:09:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:09:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:09:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:10:08","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:10:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:10:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:10:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:10:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:10:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:10:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:10:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:11:08","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:11:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:11:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:11:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:11:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:11:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:11:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:11:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:12:08","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:12:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:12:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:12:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:12:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:12:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:12:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:12:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:13:08","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:13:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:13:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:13:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:13:38","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:13:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:13:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:13:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:14:08","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:14:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:14:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:14:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:14:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:14:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:14:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:14:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:15:08","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:15:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:15:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:15:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:15:38","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:15:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:15:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:15:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:16:08","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:16:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:16:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:16:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:16:38","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:16:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:16:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:16:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:17:08","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:17:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:17:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:17:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:17:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:17:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:17:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:17:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:18:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:18:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:18:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:18:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:18:39","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:18:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:18:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:18:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:19:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:19:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:19:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:19:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:19:39","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:19:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:19:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:19:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:20:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:20:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:20:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:20:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:20:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:20:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:20:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:20:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:21:09","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:21:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:21:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:21:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:21:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:21:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:21:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:21:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:22:09","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:22:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:22:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:22:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:22:39","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:22:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:22:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:22:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:23:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:23:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:23:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:23:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:23:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:23:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:23:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:23:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:24:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:24:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:24:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:24:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:24:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:24:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:24:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:24:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:25:10","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:25:10","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:25:10 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:25:10"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:25:40","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:25:40","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:25:40 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:25:40"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:26:10","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:26:10","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:26:10 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:26:10"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:26:40","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:26:40","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:26:40 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:26:40"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"api-gateway","timestamp":"2025-07-15 22:26:54"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 22:27:17"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 22:27:17"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:27:22","url":"/health/live"}
{"contentLength":"57","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:27:22","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:27:22 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:27:22"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:27:52","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:27:52","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:27:52 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:27:52"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:28:22","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:28:22","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:28:22 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:28:22"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:28:52","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:28:52","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:28:52 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:28:52"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:29:22","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:29:22","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:29:22 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:29:22"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:29:52","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:29:52","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:29:52 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:29:52"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:30:22","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:30:22","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:30:22 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:30:22"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:30:52","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:30:52","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:30:52 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:30:52"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:31:22","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:31:22","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:31:22 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:31:22"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:31:52","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:31:52","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:31:52 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:31:52"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:31:59","url":"/health","userAgent":"axios/1.10.0"}
{"code":"ECONNREFUSED","error":"connect ECONNREFUSED ::1:3005","level":"warn","message":"Health check failed for notification-service","service":"api-gateway","timestamp":"2025-07-15 22:31:59","url":"http://localhost:3005"}
{"level":"info","message":"Health check performed","overallStatus":"degraded","responseTime":"52ms","service":"api-gateway","servicesHealthy":3,"timestamp":"2025-07-15 22:31:59","totalServices":4}
{"contentLength":"650","duration":"53ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 22:31:59","url":"/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:31:59 +0000] \"GET /health HTTP/1.1\" 503 650 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:31:59"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:32:23","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:32:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:32:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:32:23"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:32:32","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:32:32","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:32 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:32"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:32:32","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 22:32:32","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:32 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:32"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:32:32","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 22:32:32","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:32 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:32"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:32:32","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 22:32:32","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:32 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:32"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 22:32:33","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 22:32:33"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registration successful","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"578","duration":"314ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":201,"timestamp":"2025-07-15 22:32:33","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:33 +0000] \"POST /auth/register HTTP/1.1\" 201 578 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:33"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 22:32:33","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 22:32:33"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"252ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:32:33","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:33 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:33"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 22:32:33","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 22:32:33"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"108","duration":"42ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-15 22:32:33","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:22:32:33 +0000] \"POST /assessments/submit HTTP/1.1\" 500 108 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 22:32:33"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:32:53","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:32:53","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:32:53 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:32:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:33:23","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:33:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:33:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:33:23"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:33:53","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:33:53","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:33:53 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:33:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:34:23","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:34:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:34:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:34:23"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:34:53","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:34:53","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:34:53 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:34:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:35:23","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:35:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:35:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:35:23"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:35:53","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:35:53","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:35:53 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:35:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:36:23","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:36:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:36:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:36:23"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:36:53","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:36:53","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:36:53 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:36:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:37:23","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:37:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:37:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:37:23"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:37:53","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:37:53","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:37:53 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:37:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:38:23","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:38:23","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:38:23 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:38:23"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:38:54","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:38:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:38:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:38:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:39:24","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:39:24","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:39:24 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:39:24"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:39:54","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:39:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:39:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:39:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:40:24","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:40:24","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:40:24 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:40:24"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:40:54","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:40:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:40:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:40:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:41:24","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:41:24","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:41:24 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:41:24"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:41:54","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:41:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:41:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:41:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:42:24","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:42:24","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:42:24 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:42:24"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:42:54","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:42:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:42:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:42:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:43:24","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:43:24","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:43:24 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:43:24"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:43:54","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:43:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:43:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:43:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:44:24","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:44:24","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:44:24 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:44:24"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:44:54","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:44:54","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:44:54 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:44:54"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:45:25","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:45:25","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:45:25 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:45:25"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:45:55","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:45:55","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:45:55 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:45:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:46:25","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:46:25","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:46:25 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:46:25"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:46:55","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:46:55","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:46:55 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:46:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:47:25","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:47:25","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:47:25 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:47:25"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:47:55","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:47:55","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:47:55 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:47:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:48:25","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:48:25","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:48:25 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:48:25"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:48:55","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:48:55","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:48:55 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:48:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:49:25","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:49:25","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:49:25 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:49:25"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:49:55","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:49:55","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:49:55 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:49:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:50:25","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:50:25","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:50:25 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:50:25"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:50:55","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:50:55","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:50:55 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:50:55"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:51:26","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:51:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:51:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:51:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:51:56","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:51:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:51:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:51:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:52:26","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:52:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:52:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:52:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:52:56","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:52:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:52:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:52:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:53:26","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:53:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:53:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:53:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:53:56","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:53:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:53:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:53:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:54:26","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:54:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:54:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:54:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:54:56","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:54:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:54:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:54:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:55:26","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:55:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:55:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:55:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:55:56","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:55:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:55:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:55:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:56:26","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:56:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:56:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:56:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:56:56","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:56:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:56:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:56:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:57:26","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:57:26","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:57:26 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:57:26"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:57:56","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:57:56","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:57:56 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:57:56"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:58:27","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:58:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:58:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:58:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:58:57","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:58:57","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:58:57 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:58:57"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:59:27","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:59:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:59:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:59:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 22:59:57","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 22:59:57","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:22:59:57 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 22:59:57"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:00:27","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:00:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:00:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:00:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:00:57","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:00:57","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:00:57 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:00:57"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:01:27","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:01:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:01:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:01:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:01:57","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:01:57","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:01:57 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:01:57"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:02:27","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:02:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:02:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:02:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:02:57","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:02:57","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:02:57 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:02:57"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:03:27","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:03:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:03:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:03:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:03:57","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:03:57","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:03:57 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:03:57"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:04:27","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:04:27","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:04:27 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:04:27"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:04:58","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:04:58","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:04:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:04:58"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:05:28","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:05:28","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:05:28 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:05:28"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:05:58","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:05:58","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:05:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:05:58"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:06:28","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:06:28","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:06:28 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:06:28"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:06:58","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:06:58","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:06:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:06:58"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:07:28","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:07:28","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:07:28 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:07:28"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:07:58","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:07:58","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:07:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:07:58"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:19","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:08:19","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:19 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:19"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:19","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:19","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:19 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:19"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:19","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:19 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:19"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:19","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:19 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:19"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:08:19","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:08:19"}
{"contentLength":"375","duration":"23ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:19","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:19 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:19"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:28","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:08:28","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:08:28 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:08:28"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"0ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:08:41","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:41 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:41","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:41 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:41 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:41 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"contentLength":"375","duration":"15ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:41","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:41 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"256ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:08:41","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:41 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:08:41","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:08:41"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:08:43"}
{"contentLength":"114","duration":"2547ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:08:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:08:43 +0000] \"POST /assessments/submit HTTP/1.1\" 503 114 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:08:43"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:08:58","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:08:58","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:08:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:08:58"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:09:28","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:09:28","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:09:28 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:09:28"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:09:58","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:09:58","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:09:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:09:58"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:10:28","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:10:28","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:10:28 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:10:28"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"2ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:10:34","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:34 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:10:34","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:34 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:34 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:34 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"contentLength":"375","duration":"21ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:10:34","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:34 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"305ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:10:34","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:34 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:10:34","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:10:34"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:10:37"}
{"contentLength":"114","duration":"2543ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:10:37","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:10:37 +0000] \"POST /assessments/submit HTTP/1.1\" 503 114 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:10:37"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:10:59","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:10:59","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:10:59 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:10:59"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"0ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:11:17","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:17 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:11:17","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:17 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:17 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:17 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"contentLength":"375","duration":"20ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:11:17","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:17 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"258ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:11:17","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:17 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:11:17","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:11:17"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:11:20"}
{"contentLength":"114","duration":"2535ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:11:20","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:11:20 +0000] \"POST /assessments/submit HTTP/1.1\" 503 114 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:11:20"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:11:29","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:11:29","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:11:29 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:11:29"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:11:59","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:11:59","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:11:59 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:11:59"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:12:29","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:12:29","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:12:29 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:12:29"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:12:59","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:12:59","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:12:59 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:12:59"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:13:02","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:02","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"contentLength":"375","duration":"17ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:02","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"251ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:13:02","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:13:02","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"49ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:02 +0000] \"POST /assessments/submit HTTP/1.1\" 401 99 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:02"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:29","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:13:29","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:13:29 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:13:29"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:50","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:13:50","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:50 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:50"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:51","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:51","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:51 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:51","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:51 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:51","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:51 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:13:51","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"contentLength":"375","duration":"16ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:51","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:51 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:13:51","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"259ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:13:51","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:51 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:13:51","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"52ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:13:51 +0000] \"POST /assessments/submit HTTP/1.1\" 401 99 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:13:51"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:13:59","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:13:59","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:13:59 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:13:59"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:14:29","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:14:29","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:14:29 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:14:29"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:14:45","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:14:45","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"contentLength":"375","duration":"51ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:14:45","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"317ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:14:45","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:14:45","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"57ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:14:45 +0000] \"POST /assessments/submit HTTP/1.1\" 401 99 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:14:45"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:14:59","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:14:59","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:14:59 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:14:59"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:15:29","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:15:29","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:15:29 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:15:29"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:15:59","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:15:59","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:15:59 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:15:59"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:16:17","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:16:17","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"contentLength":"375","duration":"47ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:16:17","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"281ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:16:17","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:16:17","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"252","duration":"132ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":202,"timestamp":"2025-07-15 23:16:17","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:16:17 +0000] \"POST /assessments/submit HTTP/1.1\" 202 252 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:16:17"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:16:30","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:16:30","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:16:30 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:16:30"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:17:00","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:17:00","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:17:00 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:17:00"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:17:30","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:17:30","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:17:30 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:17:30"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:18:00","url":"/health/live"}
{"contentLength":"57","duration":"4ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:18:00","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:18:00 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:18:00"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:18:30","url":"/health/live"}
{"contentLength":"57","duration":"6ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:18:30","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:18:30 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:18:30"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:19:01","url":"/health/live"}
{"contentLength":"57","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:19:01","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:19:01 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:19:01"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:19:31","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:19:31","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:19:31 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:19:31"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:20:01","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:20:01","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:20:01 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:20:01"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:20:31","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:20:31","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:20:31 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:20:31"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:20:58","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:20:58","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:58 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:20:58","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:20:58","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:58 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:20:58","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:58 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:20:58","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:58 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:20:58","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:20:58","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:58 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:20:58","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:20:58"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"294ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:20:59","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:59 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:59"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:20:59","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:20:59"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"252","duration":"59ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":202,"timestamp":"2025-07-15 23:20:59","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:20:59 +0000] \"POST /assessments/submit HTTP/1.1\" 202 252 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:20:59"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:21:02","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:21:02","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:21:02 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:21:02"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:21:32","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:21:32","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:21:32 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:21:32"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:22:02","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:22:02","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:22:02 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:22:02"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:22:32","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:22:32","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:22:32 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:22:32"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:23:02","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:23:02","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:23:02 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:23:02"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:23:32","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:23:32","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:23:32 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:23:32"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:24:02","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:24:02","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:24:02 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:24:02"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:24:32","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:24:32","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:24:32 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:24:32"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:25:02","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:25:02","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:25:02 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:25:02"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:25:32","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:25:32","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:25:32 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:25:32"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:26:03","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:26:03","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:26:03 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:26:03"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:26:33","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:26:33","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:26:33 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:26:33"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:27:03","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:27:03","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:27:03 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:27:03"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:27:33","url":"/health/live"}
{"contentLength":"57","duration":"2ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:27:33","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:27:33 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:27:33"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:27:52","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:27:52","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:52 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:27:52","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:27:52","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:52 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:27:52","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:52 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:27:52","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:52 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:27:52","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"contentLength":"375","duration":"31ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:27:52","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:52 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:27:52","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:27:52"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"566","duration":"327ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:27:53","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:53 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:53"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:27:53","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"level":"info","message":"Authentication successful <NAME_EMAIL>","method":"POST","path":"/submit","service":"api-gateway","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"Proxying assessment request to: http://assessment-service:3003/assessments/submit","service":"api-gateway","timestamp":"2025-07-15 23:27:53"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"252","duration":"43ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":202,"timestamp":"2025-07-15 23:27:53","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:27:53 +0000] \"POST /assessments/submit HTTP/1.1\" 202 252 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:27:53"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:28:03","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:28:03","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:28:03 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:28:03"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:28:33","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:28:33","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:28:33 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:28:33"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:29:03","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:29:03","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:29:03 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:29:03"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:29:33","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:29:33","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:29:33 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:29:33"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:30:03","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:30:03","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:30:03 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:30:03"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:30:34","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:30:34","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:30:34 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:30:34"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:31:04","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:31:04","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:31:04 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:31:04"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:31:34","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:31:34","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:31:34 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:31:34"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"api-gateway","timestamp":"2025-07-15 23:31:42"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 23:32:39"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 23:32:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:32:43","url":"/health/live"}
{"contentLength":"57","duration":"3ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:32:43","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:32:43 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:32:43"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:32:48","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"2ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:32:48","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:48 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:48"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:32:48","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:32:48","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:48 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:48"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:32:48","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:32:48","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:48 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:48"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:32:48","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:32:48","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:48 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:48"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:32:48","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:32:48"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registration successful","service":"api-gateway","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"contentLength":"578","duration":"374ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":201,"timestamp":"2025-07-15 23:32:49","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:49 +0000] \"POST /auth/register HTTP/1.1\" 201 578 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:49"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:32:49","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:32:49"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"contentLength":"566","duration":"328ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:32:49","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:49 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:49"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:32:49","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"error":"invalid signature","ip":"::ffff:**********","level":"warn","message":"Authentication failed: Invalid token for POST /submit","service":"api-gateway","timestamp":"2025-07-15 23:32:49","userAgent":"axios/1.10.0"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:49","url":"/assessments/submit"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:32:49 +0000] \"POST /assessments/submit HTTP/1.1\" 401 82 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:32:49"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:33:13","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:33:13","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:33:13 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:33:13"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:33:44","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:33:44","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:33:44 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:33:44"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"api-gateway","timestamp":"2025-07-15 23:34:02"}
{"level":"info","message":"API Gateway running on port 3000","service":"api-gateway","timestamp":"2025-07-15 23:34:03"}
{"level":"info","message":"Environment: development","service":"api-gateway","timestamp":"2025-07-15 23:34:03"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:34:08","url":"/health/live"}
{"contentLength":"57","duration":"4ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:34:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:34:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:34:08"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/health/live","userAgent":"axios/1.10.0"}
{"contentLength":"57","duration":"1ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:34:09","url":"/health/live"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/auth/health","userAgent":"axios/1.10.0"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:34:09","url":"/auth/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"GET /auth/health HTTP/1.1\" 404 91 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/assessments/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"GET /assessments/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"ip":"::ffff:**********","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/archive/health","userAgent":"axios/1.10.0"}
{"ip":"::ffff:**********","level":"warn","message":"Authentication failed: No token provided for GET /health","service":"api-gateway","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/archive/health"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"GET /archive/health HTTP/1.1\" 401 86 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"contentLength":"103","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/auth/register","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/register","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:34:09","url":"/auth/register"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"POST /auth/register HTTP/1.1\" 400 375 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"contentLength":"64","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/auth/login","userAgent":"axios/1.10.0"}
{"level":"info","message":"Proxying auth request to: http://auth-service:3001/auth/login","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"api-gateway","timestamp":"2025-07-15 23:34:09","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"contentLength":"566","duration":"271ms","ip":"::ffff:**********","level":"info","message":"Request completed successfully","method":"POST","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:34:09","url":"/auth/login"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"POST /auth/login HTTP/1.1\" 200 566 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"contentLength":"817","contentType":"application/json","ip":"::ffff:**********","level":"info","message":"Incoming request","method":"POST","service":"api-gateway","timestamp":"2025-07-15 23:34:09","url":"/assessments/submit","userAgent":"axios/1.10.0"}
{"error":"invalid signature","ip":"::ffff:**********","level":"warn","message":"Authentication failed: Invalid token for POST /submit","service":"api-gateway","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/submit"}
{"level":"info","message":"::ffff:********** - - [15/Jul/2025:23:34:09 +0000] \"POST /assessments/submit HTTP/1.1\" 401 82 \"-\" \"axios/1.10.0\"","service":"api-gateway","timestamp":"2025-07-15 23:34:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:34:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:34:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:34:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:34:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:35:08","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:35:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:35:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:35:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:35:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:35:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:35:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:35:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:36:08","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:36:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:36:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:36:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:36:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:36:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:36:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:36:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:37:08","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:37:08","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:37:08 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:37:08"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:37:38","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:37:38","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:37:38 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:37:38"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:38:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:38:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:38:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:38:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:38:39","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:38:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:38:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:38:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:39:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:39:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:39:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:39:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:39:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:39:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:39:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:39:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:40:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:40:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:40:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:40:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:40:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:40:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:40:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:40:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:41:09","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:41:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:41:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:41:09"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:41:39","url":"/health/live"}
{"contentLength":"57","duration":"1ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:41:39","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:41:39 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:41:39"}
{"ip":"::1","level":"info","message":"Incoming request","method":"GET","service":"api-gateway","timestamp":"2025-07-15 23:42:09","url":"/health/live"}
{"contentLength":"57","duration":"0ms","ip":"::1","level":"info","message":"Request completed successfully","method":"GET","service":"api-gateway","statusCode":200,"timestamp":"2025-07-15 23:42:09","url":"/health/live"}
{"level":"info","message":"::1 - - [15/Jul/2025:23:42:09 +0000] \"GET /health/live HTTP/1.1\" 200 57 \"-\" \"-\"","service":"api-gateway","timestamp":"2025-07-15 23:42:09"}
{"contentLength":"599","duration":"79ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 08:16:14","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-16 08:16:14","url":"/unknown-route"}
