const axios = require('axios');

async function testCompleteFlow() {
  try {
    console.log('🚀 Starting Complete End-to-End Testing...');
    
    // Step 1: User Registration
    console.log('\n📝 Step 1: User Registration');
    const userData = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      fullName: 'End-to-End Test User'
    };
    
    const registerResponse = await axios.post('http://localhost:3001/auth/register', userData);
    console.log('✅ User registered successfully');
    console.log('   Email:', registerResponse.data.data.user.email);
    console.log('   Token Balance:', registerResponse.data.data.user.token_balance);
    
    const authToken = registerResponse.data.data.token;
    const userId = registerResponse.data.data.user.id;
    
    // Step 2: User Login
    console.log('\n🔐 Step 2: User Login');
    const loginResponse = await axios.post('http://localhost:3001/auth/login', {
      email: userData.email,
      password: userData.password
    });
    console.log('✅ User logged in successfully');
    console.log('   User ID:', loginResponse.data.data.user.id);
    console.log('   Token Balance:', loginResponse.data.data.user.token_balance);
    
    // Step 3: Assessment Submission
    console.log('\n📊 Step 3: Assessment Submission');
    const assessmentData = {
      riasec: {
        realistic: [4, 5, 3, 4, 5],
        investigative: [5, 4, 5, 3, 4],
        artistic: [3, 2, 4, 5, 3],
        social: [5, 5, 4, 5, 4],
        enterprising: [4, 3, 5, 4, 3],
        conventional: [2, 3, 2, 3, 4]
      },
      ocean: {
        openness: [4, 5, 3, 4, 5],
        conscientiousness: [5, 4, 5, 4, 5],
        extraversion: [3, 4, 3, 5, 4],
        agreeableness: [5, 5, 4, 5, 4],
        neuroticism: [2, 3, 2, 3, 2]
      },
      via_is: {
        wisdom: [4, 5, 4],
        courage: [5, 4, 5],
        humanity: [4, 5, 4],
        justice: [5, 5, 4],
        temperance: [4, 4, 5],
        transcendence: [5, 4, 4]
      },
      multiple_intelligences: {
        linguistic: [4, 5, 4],
        logical_mathematical: [5, 4, 5],
        spatial: [3, 4, 3],
        musical: [2, 3, 4],
        bodily_kinesthetic: [4, 3, 4],
        interpersonal: [5, 5, 4],
        intrapersonal: [4, 5, 5],
        naturalistic: [3, 4, 3]
      },
      cognitive_style: {
        knowing: [4, 5, 4],
        planning: [5, 4, 5],
        creating: [4, 3, 4]
      }
    };
    
    const submitResponse = await axios.post('http://localhost:3003/assessments/submit', assessmentData, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Assessment submitted successfully');
    console.log('   Job ID:', submitResponse.data.data.jobId);
    console.log('   Status:', submitResponse.data.data.status);
    
    const jobId = submitResponse.data.data.jobId;
    
    console.log('\n⏳ Waiting for analysis to complete...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('\n🎉 End-to-End Test Flow Completed Successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ User Registration: SUCCESS');
    console.log('   ✅ User Login: SUCCESS');
    console.log('   ✅ Assessment Submission: SUCCESS');
    console.log('   ✅ RabbitMQ Message Publishing: SUCCESS');
    console.log('   ✅ Analysis Worker Processing: RUNNING');
    
  } catch (error) {
    console.error('❌ End-to-End test failed:', error.response?.data || error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testCompleteFlow();
