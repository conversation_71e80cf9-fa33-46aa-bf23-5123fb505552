// Auth Service Unit Tests
const request = require('supertest');

// Mock dependencies before importing app
jest.mock('../src/services/userService', () => ({
  getUserById: jest.fn(),
  updateUserProfile: jest.fn(),
  updateTokenBalance: jest.fn(),
  getTokenBalance: jest.fn()
}));

jest.mock('../src/services/authService', () => ({
  registerUser: jest.fn(),
  loginUser: jest.fn(),
  verifyUserToken: jest.fn(),
  changePassword: jest.fn()
}));

jest.mock('../src/utils/jwt', () => ({
  generateToken: jest.fn(),
  verifyToken: jest.fn()
}));

jest.mock('../src/models', () => ({
  User: {
    findByPk: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn()
  }
}));

const app = require('../src/app');
const userService = require('../src/services/userService');
const authService = require('../src/services/authService');

describe('Auth Service Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // Close any open handles
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  describe('POST /auth/register', () => {
    it('should register user successfully', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        fullName: 'Test User'
      };

      const mockResponse = {
        user: {
          id: 'user-123',
          email: userData.email,
          fullName: userData.fullName,
          token_balance: 3
        },
        token: 'mock.jwt.token'
      };

      authService.registerUser.mockResolvedValue(mockResponse);

      // Act
      const response = await request(app)
        .post('/auth/register')
        .send(userData);

      // Assert
      expect(response.status).toBe(201);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: {
            id: mockResponse.user.id,
            email: mockResponse.user.email,
            token_balance: mockResponse.user.token_balance
          },
          token: mockResponse.token
        }
      });

      expect(authService.registerUser).toHaveBeenCalledWith(userData);
    });

    it('should handle duplicate email error', async () => {
      // Arrange
      const userData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
        fullName: 'Test User'
      };

      authService.registerUser.mockRejectedValue(new Error('Email already exists'));

      // Act
      const response = await request(app)
        .post('/auth/register')
        .send(userData);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body).toMatchObject({
        success: false,
        error: {
          message: expect.stringContaining('Email already exists')
        }
      });
    });

    it('should validate required fields', async () => {
      // Arrange
      const incompleteData = {
        email: '<EMAIL>'
        // Missing password and fullName
      };

      // Act
      const response = await request(app)
        .post('/auth/register')
        .send(incompleteData);

      // Assert
      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /auth/login', () => {
    it('should login user successfully', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockResponse = {
        user: {
          id: 'user-123',
          email: loginData.email,
          fullName: 'Test User',
          token_balance: 5
        },
        token: 'mock.jwt.token'
      };

      authService.loginUser.mockResolvedValue(mockResponse);

      // Act
      const response = await request(app)
        .post('/auth/login')
        .send(loginData);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: mockResponse.user,
          token: mockResponse.token
        }
      });

      expect(authService.loginUser).toHaveBeenCalledWith(loginData);
    });

    it('should handle invalid credentials', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      authService.loginUser.mockRejectedValue(new Error('Invalid email or password'));

      // Act
      const response = await request(app)
        .post('/auth/login')
        .send(loginData);

      // Assert
      expect(response.status).toBe(401);
      expect(response.body).toMatchObject({
        success: false,
        error: {
          message: expect.stringContaining('Invalid email or password')
        }
      });
    });
  });

  describe('GET /auth/profile', () => {
    it('should get user profile with valid token', async () => {
      // Arrange
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        fullName: 'Test User',
        token_balance: 3
      };

      const mockToken = 'valid.jwt.token';

      authService.verifyUserToken.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .get('/auth/profile')
        .set('Authorization', `Bearer ${mockToken}`);

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: mockUser
        }
      });
    });

    it('should handle missing token', async () => {
      // Act
      const response = await request(app)
        .get('/auth/profile');

      // Assert
      expect(response.status).toBe(401);
      expect(response.body).toMatchObject({
        success: false,
        error: {
          message: expect.stringContaining('token')
        }
      });
    });
  });

  describe('PUT /auth/token-balance', () => {
    it('should update token balance successfully (internal service)', async () => {
      // Arrange
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        token_balance: 4
      };

      userService.updateTokenBalance.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .put('/auth/token-balance')
        .set('X-Service-Key', process.env.INTERNAL_SERVICE_KEY || 'test_internal_service_key_for_local_testing')
        .set('X-Internal-Service', 'true')
        .send({
          userId: 'user-123',
          tokenBalance: 4
        });

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          user: mockUser
        }
      });

      expect(userService.updateTokenBalance).toHaveBeenCalledWith('user-123', 4);
    });

    it('should handle unauthorized access', async () => {
      // Act
      const response = await request(app)
        .put('/auth/token-balance')
        .send({
          userId: 'user-123',
          tokenBalance: 4
        });

      // Assert
      expect(response.status).toBe(401);
      expect(response.body).toMatchObject({
        success: false,
        error: {
          message: expect.stringContaining('Unauthorized')
        }
      });
    });
  });
});
