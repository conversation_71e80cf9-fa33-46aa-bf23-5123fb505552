// Local testing setup with mocked dependencies
process.env.NODE_ENV = 'test';
process.env.MOCK_DATABASE = 'true';
process.env.MOCK_RABBITMQ = 'true';
process.env.MOCK_GOOGLE_AI = 'true';

// Database configuration for local testing
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_NAME = 'atma_test_db_local';
process.env.DB_USER = 'atma_user';
process.env.DB_PASSWORD = 'secret-passworrd';
process.env.DB_DIALECT = 'postgres';

// RabbitMQ configuration for local testing
process.env.RABBITMQ_URL = 'amqp://localhost:5672';
process.env.RABBITMQ_USER = 'guest';
process.env.RABBITMQ_PASSWORD = 'guest';
process.env.EXCHANGE_NAME = 'atma_test_exchange';
process.env.QUEUE_NAME = 'test_assessment_analysis';
process.env.ROUTING_KEY = 'test.analysis.process';
process.env.DEAD_LETTER_QUEUE = 'test_assessment_analysis_dlq';

// Service URLs for local testing
process.env.AUTH_SERVICE_URL = 'http://localhost:3001';
process.env.ARCHIVE_SERVICE_URL = 'http://localhost:3002';
process.env.ASSESSMENT_SERVICE_URL = 'http://localhost:3003';
process.env.NOTIFICATION_SERVICE_URL = 'http://localhost:3005';

// Security configuration
process.env.JWT_SECRET = 'test_jwt_secret_key_for_local_testing_only';
process.env.INTERNAL_SERVICE_KEY = 'test_internal_service_key_for_local_testing';
process.env.JWT_EXPIRES_IN = '1h';
process.env.BCRYPT_ROUNDS = '4'; // Reduced for faster testing

// Google AI configuration (mocked)
process.env.GOOGLE_AI_API_KEY = 'test_google_ai_api_key_for_testing';
process.env.GOOGLE_AI_MODEL = 'gemini-2.5-flash';
process.env.AI_TEMPERATURE = '0.7';
process.env.AI_MAX_TOKENS = '4096';

// Worker configuration
process.env.WORKER_CONCURRENCY = '1';
process.env.MAX_RETRIES = '2';
process.env.RETRY_DELAY = '1000';
process.env.PROCESSING_TIMEOUT = '30000';

// Token configuration
process.env.DEFAULT_TOKEN_BALANCE = '5';
process.env.ANALYSIS_TOKEN_COST = '1';

// Logging configuration (reduced for testing)
process.env.LOG_LEVEL = 'error';
process.env.LOG_FILE = 'logs/test-local.log';

// Mock console for cleaner output
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  warn: console.warn,
  error: console.error
};

// Global test utilities
global.testUtils = {
  generateTestUser: (overrides = {}) => ({
    id: `test-user-${Date.now()}`,
    email: `test-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    fullName: 'Test User',
    tokenBalance: 5,
    ...overrides
  }),

  generateTestAssessment: () => ({
    riasec: {
      realistic: [4, 5, 3, 4, 5],
      investigative: [5, 4, 5, 3, 4],
      artistic: [3, 2, 4, 5, 3],
      social: [5, 5, 4, 5, 4],
      enterprising: [4, 3, 5, 4, 3],
      conventional: [2, 3, 2, 3, 4]
    },
    ocean: {
      openness: [4, 5, 3, 4, 5],
      conscientiousness: [5, 4, 5, 4, 5],
      extraversion: [3, 4, 3, 5, 4],
      agreeableness: [5, 5, 4, 5, 4],
      neuroticism: [2, 3, 2, 3, 2]
    },
    via_is: {
      wisdom: [4, 5, 4],
      courage: [5, 4, 5],
      humanity: [4, 5, 4],
      justice: [5, 5, 4],
      temperance: [4, 4, 5],
      transcendence: [5, 4, 4]
    },
    multiple_intelligences: {
      linguistic: [4, 5, 4],
      logical_mathematical: [5, 4, 5],
      spatial: [3, 4, 3],
      musical: [2, 3, 4],
      bodily_kinesthetic: [4, 3, 4],
      interpersonal: [5, 5, 4],
      intrapersonal: [4, 5, 5],
      naturalistic: [3, 4, 3]
    },
    cognitive_style: {
      knowing: [4, 5, 4],
      planning: [5, 4, 5],
      creating: [4, 3, 4]
    }
  }),

  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
};

// Custom Jest matchers
expect.extend({
  toBeValidJWT(received) {
    const jwt = require('jsonwebtoken');
    try {
      jwt.verify(received, process.env.JWT_SECRET);
      return { pass: true, message: () => `Expected ${received} not to be a valid JWT` };
    } catch (error) {
      return { pass: false, message: () => `Expected ${received} to be a valid JWT` };
    }
  },

  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    return {
      pass,
      message: () => pass 
        ? `Expected ${received} not to be a valid UUID`
        : `Expected ${received} to be a valid UUID`
    };
  }
});

console.log('Local testing environment setup completed');
